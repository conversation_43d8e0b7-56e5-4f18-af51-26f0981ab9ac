use mongodb::{Client, Collection};

use crate::{api::LlmSave, todo::Todo};


pub async fn get_todo_collection() -> mongodb::error::Result<Collection<Todo>> {
    const DB_NAME: &str = "todo_db";
    const COLLECTION_NAME: &str = "todos";
    // For local MongoDB
    let uri = "mongodb://localhost:27017";
    
    let client = Client::with_uri_str(uri).await?;
    let database = client.database(DB_NAME);
    let collection = database.collection::<Todo>(COLLECTION_NAME);
    
    Ok(collection)
}


pub async fn get_llm_collection() -> mongodb::error::Result<Collection<LlmSave>> {
    const DB_NAME: &str = "llm_db";
    const COLLECTION_NAME: &str = "llm_responses";
    // For local MongoDB
    let uri = "mongodb://localhost:27017";
    
    let client = Client::with_uri_str(uri).await?;
    let database = client.database(DB_NAME);
    let collection = database.collection::<LlmSave>(COLLECTION_NAME);
    
    Ok(collection)
}
#![windows_subsystem = "windows"]

use iced::widget::text_input::{focus, Id};
use iced::window::{self, Level, Position};
use iced::Point;

mod api;
mod colors;
mod icons;
mod models;
mod mongo_connection;
mod theme;
mod todo;
pub mod update;
mod view;
use update::update;
use view::view;

use crate::models::{AppState, LlmState, Provider};


fn main() -> iced::Result {
    let default_state = AppState {
        llm_state: LlmState {
        model: Provider::default().default_model(),
        ..Default::default()
        },
        ..Default::default()
    };

    iced::application("YUMLABS Assist", update, view)
        .window(window::Settings {
            size: iced::Size {
                width: 500.0,
                height: 500.0,
            },
            resizable: false,
            position: Position::Specific(Point {
                x: 1400.0,
                y: 530.0,
            }),
            level: Level::AlwaysOnTop,
            decorations: false,
            transparent: true,
            ..Default::default()
        })
        .run_with(|| (default_state, focus(Id::new("query_input"))))
}

# Developer Documentation for Yumlabs

## Project Overview
Yumlabs is a Rust-based GUI application built with the Iced framework, serving as an LLM Query Widget for querying Perplexity and Gemini APIs. It provides a user-friendly interface for inputting queries, selecting providers/models, displaying responses, and managing interactions like copying to clipboard or clearing data. The app runs in a transparent, always-on-top window positioned in the bottom-right corner (hardcoded for 1920x1080 screens).

Key features:
- Query input with submit on Enter, focused on app startup.
- Provider/model selection via pick lists.
- Todo list management (create, complete, delete).
- Async API calls with loading states.
- Response display in a scrollable area.
- Error handling and display.
- Clipboard copy and clear functions.
- Close button to exit.

Tech stack: Rust 2021 edition, Iced 0.13 for GUI, Reqwest for HTTP, Tokio for async runtime, Serde for JSON, Arboard for clipboard, Dotenv for environment variables.

## Architecture
The project follows a modular, MVC-inspired structure with separation of concerns:
- **State Management**: `models.rs` defines a main `AppState` struct and smaller, view-specific state structs (`LlmState`, `TodoState`). Messages are nested (`LlmMessage`, `TodoMessage`) for better organization.
- **Logic**: `update.rs` handles message processing and side effects via Iced Tasks.
- **UI Rendering**: `view.rs` builds the widget tree using Iced components.
- **UI Rendering**: The `view` module (`src/view/`) builds the widget tree. `mod.rs` is the entry point, delegating to sub-modules for each specific view (`llm_view.rs`, `todo_view.rs`, etc.).
- **Database Models**: `todo.rs` defines the Todo struct and functions for database interaction.
- **Styling**: `theme.rs` provides custom styles for a glassy, dark theme.
- **API Handling**: `api.rs` manages external LLM API interactions asynchronously.
- **Entry Point**: `main.rs` orchestrates the Iced application setup.

Async operations use Tokio; errors propagate via Result types; UI updates via immutable state mutations.

## Modules Breakdown

### main.rs
Entry point (src/main.rs). Sets up the Iced application with window settings and initial state.

- **Imports**: `iced::window::{self, Level, Position}`, `iced::{Task, Point}`, and local modules.
- **main()**: Creates default `LlmWidget` state, configures window (size 300x550, position bottom-right, always-on-top, no decorations, transparent), runs app with initial task to focus the query input field.
- **Purpose**: Bootstraps the GUI loop; sets up initial focus for user convenience.

### models.rs
Core data models (src/models.rs). Defines state, enums, and messages for the app.

- **`AppState` Struct**: The single source of truth for the application. It holds the `current_view` and instances of view-specific states like `LlmState` and `TodoState`. It also contains global fields like `is_loading` and `error`.
- **View-Specific States**: Structs like `LlmState` and `TodoState` hold data relevant only to their corresponding views.
- **`Message` Enum**: A nested enum structure. The top-level `Message` handles global events and navigation, while sub-enums like `LlmMessage` and `TodoMessage` contain actions specific to a view.
- **Purpose**: To create a modular and scalable state management system where state and logic are co-located by feature.

### update.rs
The `update` module is responsible for all state mutations.

- **`update/mod.rs`**: The main entry point. The `update` function acts as a router, delegating messages to the appropriate sub-updater based on the message variant (e.g., `Message::Llm` goes to `llm_updater`).
- **`update/llm_updater.rs`**, **`update/todo_updater.rs`**: Each file handles the logic for its specific part of the state. They receive the relevant `Message` variant and a mutable reference to the main `AppState`.
- **Purpose**: To break down the application logic into manageable, feature-specific chunks, improving readability and maintainability.

### view.rs
The `view` module is responsible for UI rendering.

- **`view/mod.rs`**: The main entry point. Contains the top-level `view` function that acts as a router, selecting which sub-view to render based on the application's current state (`LlmWidget.current_view`).
- **`view/llm_view.rs`**, **`view/todo_view.rs`**, etc.: Each file is responsible for building a specific screen of the application. They contain the layout logic and widget definitions for their respective views.
- **`view/components.rs`**: Contains reusable UI components, such as `create_button` and `build_error_display`, that are shared across multiple views.
- **Purpose**: To create a modular and maintainable UI layer by separating the concerns of each view.

### theme.rs
Custom styling (src/theme.rs). Defines colors and style functions for a dark, glassy theme.

- **Constants** (lines 3-13): Colors like PANEL (glassy), TEXT, GOLD (buttons), TEAL (focus).
- **panel()** (lines 33-35): Container style with background, border, shadow.
- **primary_button()** (lines 38-73): Gold button with hover/press states, shadow.
- **secondary_button()** (lines 76-99): Dark button with teal hover border.
- **input()** (lines 102-119): Text input with focus ring.
- **picker()** (lines 122-135): Pick list styled like input.
- **scroller()** (lines 138-170): Scrollable with container and rail styles.
- **Purpose**: Consistent theming; functions return closures for Iced styling.

### api.rs
API interaction (src/api.rs). Handles async LLM queries.

- **Imports**: Serde, dotenv, std::env.
- **get_api_key()** (lines 7-22): Loads dotenv, checks env for PERPLEXITY_API_KEY or GEMINI_API_KEY.
- **Structs**: ChatCompletionRequest/MessageReq (Perplexity), ChatCompletionResponse/Choice/MessageResp (Perplexity response); GeminiRequest/Content/Part (Gemini), GeminiResponse/Candidate/ContentResp/PartResp (Gemini response).
- **fetch_llm_response()** (lines 86-173): Async fn taking query, provider, model.
  - Loads dotenv, creates reqwest client.
  - Match provider:
    - Perplexity: Gets API key, builds ChatCompletionRequest, POST to api.perplexity.ai, parses response.
    - Gemini: Gets API key, builds GeminiRequest, POST to generativelanguage.googleapis.com with model, parses response.
  - Returns Result<String, String> with content or error.
- **Purpose**: Encapsulates HTTP logic; error handling for keys, requests, parsing.

## Dependencies
From Cargo.toml:
- iced 0.13 (tokio feature): GUI framework.
- reqwest 0.12 (json): HTTP client.
- serde 1.0 (derive): Serialization.
- arboard 3.4.0: Clipboard.
- tokio 1 (full): Async runtime.
- dotenv 0.15.0: Env loading.

## Configuration
- API keys: Set PERPLEXITY_API_KEY and GEMINI_API_KEY in .env file.
- Build: `cargo build`.
- Run: `cargo run` (loads .env automatically).
- Development: Install cargo-watch with `cargo install cargo-watch`, then use `cargo watch -x run` for auto-rebuild and run on file changes.
- No linting specified; use `cargo clippy` for checks.

## Key Patterns
- Async: Tokio for API calls, Iced Tasks for side effects.
- Error Handling: Result types, propagated to UI.
- State: Immutable updates via messages.
- UI: Declarative, themed widgets.
- Modularity: Clear separation for maintainability.

## Traversal Tips for AI
- Start with models.rs for state understanding.
- Follow message flow: view -> update -> api.
- Read structs/impls first, then functions.
- File refs: e.g., src/models.rs:2 for LlmWidget.
- Focus on logic in update.rs, UI in view.rs, styles in theme.rs.
use iced::{
    widget::{
        button, checkbox, column, container, row, scrollable, text, text_input,
    },
    Alignment, Element, Length,
};

use crate::{
    icons,
    models::{Message, TodoMessage, TodoState, View},
    theme,
};

use super::components::{create_button, build_error_display};

pub fn build_todo_view<'a>(state: &'a TodoState, is_loading: bool, error: &'a str) -> Element<'a, Message> {
    let home_button = create_button(
        icons::close(), // Using close icon for "back"
        "Back to Main",
        Some(Message::Navigate(View::Llm)),
        theme::panel_button(),
    );

    let title = text("Todo List").size(20);

    let new_todo_input = text_input("What needs to be done?", &state.new_todo_title)
        .on_input(|s| Message::Todo(TodoMessage::NewTitleChanged(s)))
        .on_submit(Message::Todo(TodoMessage::Add))
        .padding(10);

    let add_todo_button = button(text("Add"))
        .on_press(Message::Todo(TodoMessage::Add))
        .padding(10);

    let input_row = row![new_todo_input, add_todo_button]
        .spacing(10)
        .align_y(Alignment::Center);

    let todos_list = if is_loading {
        column![text("Loading...")]
    } else {
        state
            .todos
            .iter()
            .fold(column![], |col, todo| {
                let delete_button = button(text("Delete").size(12))
                    .on_press(Message::Todo(TodoMessage::Delete(todo.id.unwrap())))
                    .padding(5)
                    .style(theme::panel_button());

                let checkbox = checkbox(&todo.title, todo.completed)
                    .on_toggle(move |_| Message::Todo(TodoMessage::ToggleCompletion(todo.id.unwrap())));

                col.push(row![checkbox, delete_button].spacing(100).align_y(Alignment::Center))
            })
            .spacing(10)
    };

    let content = column![
        row![home_button].align_y(Alignment::Center),
        title,
        input_row,
        scrollable(container(todos_list).width(Length::Fill).padding(10)).height(Length::Fill),
        build_error_display(error),
    ]
    .spacing(15)
    .padding(20)
    .align_x(Alignment::Center);

    container(content)
        .width(Length::Fill)
        .height(Length::Fill)
        .into()
}
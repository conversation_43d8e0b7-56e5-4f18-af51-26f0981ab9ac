use crate::models::*;
use crate::{icons, theme};
use iced::{
    widget::{
        button,
        checkbox,
        column,
        container,
        pick_list,
        row,
        scrollable,
        text,
        text_input,
        text_input::Id,
        image,
        tooltip,
    },
    Alignment, Element, Length,
};

pub fn view(state: &'_ LlmWidget) -> Element<'_, Message> {
    let content = match state.current_view {
        View::Llm => build_llm_view(state),
        View::Tools => build_tools_view(),
        View::Todos => build_todo_view(state),
    };

    container(content)
        .width(Length::Fill)
        .height(Length::Fill)
        .center_x(Length::Fill)
        .center_y(Length::Fill)
        .style(theme::panel())
        .into()
}

fn build_llm_view(state: &'_ LlmWidget) -> Element<'_, Message> {
    column![
        text("YUMLABS").size(15),
        build_provider_model_pickers(state),
        build_input_field(state),
        build_button_controls(state),
        text("Response:").size(14),
        build_response_display(state),
        build_error_display(&state.error),
    ]
    .spacing(15)
    .padding(20)
    .align_x(Alignment::Center)
    .into()
}

fn build_tools_view<'a>() -> Element<'a, Message> {
    let home_button = create_button(
        icons::close(), // Using close icon for "back"
        "Back to Main",
        Some(Message::Home),
        theme::panel_button(),
    );

    let vs_code_button = create_button(
        icons::tools(),
        "Open VS Code",
        Some(Message::VSCode),
        theme::panel_button(),
    );

    let todos_btn = create_button(
        icons::tools(), // Placeholder icon
        "Open Todos",
        Some(Message::Todos),
        theme::panel_button(),
    );

    


    let mut controls = Vec::new();
    controls.push(Element::from(todos_btn));
    controls.push(Element::from(vs_code_button));

    

    let grid = column(controls).spacing(5);

    column![
        row![home_button].align_y(Alignment::Center),
        text("Tools Grid").size(15),
        grid,
    ]
    .spacing(15)
    .padding(20)
    .align_x(Alignment::Center)
    .into()
}

fn build_todo_view(state: &'_ LlmWidget) -> Element<'_, Message> {
    let home_button = create_button(
        icons::close(), // Using close icon for "back"
        "Back to Main",
        Some(Message::Home),
        theme::panel_button(),
    );

    let title = text("Todo List").size(20);

    let new_todo_input = text_input("What needs to be done?", &state.new_todo_title)
        .on_input(Message::NewTodoTitleChanged)
        .on_submit(Message::AddTodo)
        .padding(10);

    let add_todo_button = button(text("Add"))
        .on_press(Message::AddTodo)
        .padding(10);

    let input_row = row![new_todo_input, add_todo_button].spacing(10).align_y(Alignment::Center);

    let todos_list = if state.is_loading {
        column![text("Loading...")]
    } else {
        state.todos.iter().fold(column![], |col, todo| {
            let delete_button = button(text("Delete").size(12))
                .on_press(Message::DeleteTodo(todo.id.unwrap()))
                .padding(5)            
                .style(theme::panel_button());

            let checkbox = checkbox(&todo.title, todo.completed)
                .on_toggle(move |_| Message::ToggleTodoCompletion(todo.id.unwrap()));

            col.push(row![checkbox, delete_button].spacing(100).align_y(Alignment::Center))
        }).spacing(10)
    };

    let content = column![
        row![home_button].align_y(Alignment::Center),
        title,
        input_row,
        scrollable(
            container(todos_list)
                .width(Length::Fill)
                .padding(10)
        )
        .height(Length::Fill),
        build_error_display(&state.error),
    ]
    .spacing(15)
    .padding(20)
    .align_x(Alignment::Center);

    container(content)
        .width(Length::Fill)
        .height(Length::Fill)
        .into()
}

fn build_provider_model_pickers(state: &'_ LlmWidget) -> Element<'_, Message> {
    let providers: Vec<_> = [Provider::Perplexity, Provider::Gemini]
        .iter()
        .map(|p| PickerOption::new(*p))
        .collect();
    let selected_provider = providers
        .iter()
        .find(|p| p.value == state.provider)
        .cloned();
    let provider_picker = pick_list(providers, selected_provider, |p| {
        Message::ProviderChanged(p.value)
    })
    .style(theme::picker())
    .menu_style(theme::picker_menu());

    let models: Vec<_> = state
        .provider
        .available_models()
        .into_iter()
        .map(PickerOption::new)
        .collect();
    let selected_model = models.iter().find(|m| m.value == state.model).cloned();
    let model_picker = pick_list(models, selected_model, |m| Message::ModelChanged(m.value))
        .style(theme::picker())
        .menu_style(theme::picker_menu());

    column![
        row![text("Provider:").size(14), provider_picker]
            .spacing(10)
            .align_y(Alignment::Center),
        row![text("Model:").size(14), model_picker]
            .spacing(10)
            .align_y(Alignment::Center),
    ]
    .spacing(10)
    .into()
}

fn build_input_field(state: &'_ LlmWidget) -> Element<'_, Message> {
    text_input("Type your question...", &state.query)
        .id(Id::new("query_input"))
        .on_input(if state.is_loading {
            |_| Message::Ignore
        } else {
            Message::QueryChanged
        })
        .on_submit(if state.is_loading {
            Message::Ignore
        } else {
            Message::Send
        })
        .padding(10)
        .size(16)
        .width(Length::Fill)
        .style(theme::input())
        .into()
}

fn build_button_controls(state: &'_ LlmWidget) -> Element<'_, Message> {
    let send_btn = create_button(
        icons::send(),
        "Send Query",
        if state.is_loading {
            None
        } else {
            Some(Message::Send)
        },
        theme::panel_button(),
    );

    let copy_btn = create_button(
        icons::copy(),
        "Copy Response",
        Some(Message::Copy),
        theme::panel_button(),
    );

    let clear_btn = create_button(
        icons::clear(),
        "Clear All",
        Some(Message::Clear),
        theme::panel_button(),
    );

    let close_btn = create_button(
        icons::close(),
        "Close App",
        Some(Message::Close),
        theme::panel_button(),
    );

    let twitch_btn = create_button(
        icons::twitch(),
        "Open Twitch",
        Some(Message::Twitch),
        theme::panel_button(),
    );

    let perplexity_btn = create_button(
        icons::perplexity(),
        "Open Perplexity",
        Some(Message::Perplexity),
        theme::panel_button(),
    );
    let claude_btn = create_button(
        icons::claude(),
        "Open Claude",
        Some(Message::Claude),
        theme::panel_button(),
    );
    let github_btn = create_button(
        icons::github(),
        "Open GitHub",
        Some(Message::Github),
        theme::panel_button(),
    );
    let youtube_btn = create_button(
        icons::youtube(),
        "Open YouTube",
        Some(Message::Youtube),
        theme::panel_button(),
    );
    let kick_btn = create_button(
        icons::kick(),
        "Open Kick",
        Some(Message::Kick),
        theme::panel_button(),
    );

    let tools_btn = create_button(
        icons::tools(),
        "Open Tools",
        Some(Message::Tools),
        theme::panel_button(),
    );

 

    let primary_controls = row![send_btn, copy_btn, clear_btn, tools_btn, close_btn]
        .spacing(10)
        .align_y(Alignment::Center);

    let secondary_controls = row![
        twitch_btn,
        perplexity_btn,
        claude_btn,
        github_btn,
        youtube_btn,
        kick_btn
    ]
    .spacing(10)
    .align_y(Alignment::Center);
    
    column![primary_controls, secondary_controls]
        .spacing(10)
        .align_x(Alignment::Center)
        .into()
}

fn create_button<'a>(
    icon: image::Image,
    tooltip_text: &'static str,
    on_press: Option<Message>,
    style: impl Fn(&iced::Theme, button::Status) -> button::Style + 'a,
) -> Element<'a, Message> {
    let btn = button(icon).padding(10).height(50).style(style);

    let btn_with_press = if let Some(msg) = on_press {
        btn.on_press(msg)
    } else {
        btn
    };

    tooltip(btn_with_press, tooltip_text, tooltip::Position::Bottom)
        .style(theme::tooltip())
        .into()
}

fn build_response_display(state: &'_ LlmWidget) -> Element<'_, Message> {
    let response_text = if state.is_loading {
        "  Loading...".to_string()
    } else if !state.response.is_empty() {
        state.response.clone()
    } else {
        "  Response will appear here...".to_string()
    };

    scrollable(text(response_text).size(14))
        .height(Length::Fixed(250.0))
        .width(Length::Fill)
        .style(theme::scroller())
        .into()
}

fn build_error_display(error: &'_ str) -> Element<'_, Message> {
    if !error.is_empty() {
        text(error)
            .size(14)
            .style(|_theme| text::Style {
                color: Some(iced::Color::from_rgb(0.8, 0.2, 0.2)),
            })
            .into()
    } else {
        text("").into()
    }
}

use iced::widget::image::{self, <PERSON><PERSON>};

// https://www.flaticon.com/
// https://lobehub.com/icons?q=

const ICON_WIDTH: u16 = 20;
const ICON_HEIGHT: u16 = 20;

fn load_icon_from_memory(bytes: &'static [u8]) -> image::Image {
    image::Image::new(Handle::from_bytes(bytes))
        .width(ICON_WIDTH)
        .height(ICON_HEIGHT)
}

pub fn send() -> image::Image {
    load_icon_from_memory(include_bytes!("../images/send.png"))
}
pub fn copy() -> image::Image {
    load_icon_from_memory(include_bytes!("../images/copy.png"))
}
pub fn clear() -> image::Image {
    load_icon_from_memory(include_bytes!("../images/clear.png"))
}
pub fn close() -> image::Image {
    load_icon_from_memory(include_bytes!("../images/close.png"))
}
pub fn twitch() -> image::Image {
    load_icon_from_memory(include_bytes!("../images/twitch.png"))
}
pub fn perplexity() -> image::Image {
    load_icon_from_memory(include_bytes!("../images/perplexity.png"))
}
pub fn claude() -> image::Image {
    load_icon_from_memory(include_bytes!("../images/claude.png"))
}
pub fn youtube() -> image::Image {
    load_icon_from_memory(include_bytes!("../images/youtube.png"))
}
pub fn github() -> image::Image {
    load_icon_from_memory(include_bytes!("../images/github.png"))
}
pub fn kick() -> image::Image {
    load_icon_from_memory(include_bytes!("../images/kick.png"))
}

pub fn tools() -> image::Image {
    load_icon_from_memory(include_bytes!("../images/tools.png"))
}

use crate::{
    api::{fetch_llm_response, save_llm_response},
    models::{AppState, LlmMessage, Message},
};
use iced::Task;

pub fn llm_update(state: &mut AppState, message: LlmMessage) -> Task<Message> {
    match message {
        LlmMessage::QueryChanged(query) => {
            state.llm_state.query = query;
            Task::none()
        }
        LlmMessage::ProviderChanged(provider) => {
            state.llm_state.provider = provider;
            state.llm_state.model = provider.default_model();
            Task::none()
        }
        LlmMessage::ModelChanged(model) => {
            state.llm_state.model = model;
            Task::none()
        }
        LlmMessage::Send => {
            if state.llm_state.query.trim().is_empty() {
                state.error = "Please enter a question".to_string();
                return Task::none();
            }
            state.is_loading = true;
            state.error.clear();
            let query = state.llm_state.query.clone();
            let provider = state.llm_state.provider;
            let model = state.llm_state.model.clone();
            Task::perform(
                fetch_llm_response(query, provider, model),
                |res| Message::Llm(LlmMessage::ResponseReceived(res)),
            )
        }
        LlmMessage::ResponseReceived(Ok(response)) => {
            state.llm_state.response = response;

            // Save the response to the database
            save_llm_response(
                state.llm_state.query.clone(),
                state.llm_state.response.clone(),
                state.llm_state.provider.to_string(),
                state.llm_state.model.clone(),
            );

            state.error.clear();
            state.is_loading = false;
            Task::none()
        }
        LlmMessage::ResponseReceived(Err(e)) => {
            state.error = format!("Error: {}", e);
            state.llm_state.response.clear();
            state.is_loading = false;
            Task::none()
        }
    }
}
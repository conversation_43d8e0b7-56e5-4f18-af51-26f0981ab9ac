use iced::{border, theme, Background, Color, Shadow, Vector};

use crate::colors::*;

fn panel_style() -> iced::widget::container::Style {
    iced::widget::container::Style {
        background: Some(Background::Color(PANEL)),
        text_color: Some(TEXT),
        border: border::Border {
            color: PANEL_EDGE,
            width: 1.0,
            radius: border::Radius::from(12.0),
        },
        shadow: Shadow {
            color: PANEL_SHADOW,
            offset: Vector::new(0.0, 8.0),
            blur_radius: 24.0,
        },
        ..Default::default()
    }
}

// Framed panel container (main card)
pub fn panel() -> impl Fn(&theme::Theme) -> iced::widget::container::Style {
    |_theme| panel_style()
}

// Panel button
pub fn panel_button(
) -> impl Fn(&theme::Theme, iced::widget::button::Status) -> iced::widget::button::Style {
    |_, status| {
        let mut base = iced::widget::button::Style {
            background: Some(Background::Color(BUTTON_BG)),
            text_color: TEXT,
            border: border::Border {
                color: PANEL_EDGE,
                width: 1.0,
                radius: border::Radius::from(10.0),
            },
            shadow: Shadow {
                color: BUTTON_SHADOW,
                offset: Vector::new(0.0, 3.0),
                blur_radius: 14.0,
            },
            ..Default::default()
        };
        if matches!(status, iced::widget::button::Status::Hovered) {
            base.border.color = TEAL;
        }
        base
    }
}

// Text input with teal focus ring
pub fn input(
) -> impl Fn(&theme::Theme, iced::widget::text_input::Status) -> iced::widget::text_input::Style {
    |_, status| {
        let focused = matches!(status, iced::widget::text_input::Status::Focused);
        iced::widget::text_input::Style {
            background: Background::Color(INPUT_BG),
            border: border::Border {
                color: if focused { TEAL } else { PANEL_EDGE },
                width: if focused { 2.0 } else { 1.0 },
                radius: border::Radius::from(10.0),
            },
            icon: MUTED,
            placeholder: MUTED,
            value: TEXT,
            selection: SELECTION,
        }
    }
}

// Pick list styled like inputs
pub fn picker(
) -> impl Fn(&theme::Theme, iced::widget::pick_list::Status) -> iced::widget::pick_list::Style {
    |_, _| iced::widget::pick_list::Style {
        text_color: TEXT,
        placeholder_color: MUTED,
        background: Background::Color(INPUT_BG),
        border: border::Border {
            color: PANEL_EDGE,
            width: 1.0,
            radius: border::Radius::from(10.0),
        },
        handle_color: MUTED,
    }
}

// Menu style for pick lists
pub fn picker_menu() -> impl Fn(&theme::Theme) -> iced::overlay::menu::Style {
    |_| iced::overlay::menu::Style {
        background: Background::Color(PANEL),
        border: border::Border {
            color: PANEL_EDGE,
            width: 1.0,
            radius: border::Radius::from(6.0),
        },
        text_color: TEXT,
        selected_background: Background::Color(Color::from_rgba(TEAL.r, TEAL.g, TEAL.b, 0.5)),
        selected_text_color: TEXT,
    }
}

// Scrollable with subtle edge and thumb
pub fn scroller(
) -> impl Fn(&theme::Theme, iced::widget::scrollable::Status) -> iced::widget::scrollable::Style {
    |_, _| iced::widget::scrollable::Style {
        container: iced::widget::container::Style {
            background: Some(Background::Color(SCROLLER_BG)),
            text_color: Some(TEXT),
            border: border::Border {
                color: PANEL_EDGE,
                width: 1.0,
                radius: border::Radius::from(10.0),
            },
            shadow: Shadow::default(),
            ..Default::default()
        },
        gap: None,
        vertical_rail: iced::widget::scrollable::Rail {
            background: None,
            border: border::Border::default(),
            scroller: iced::widget::scrollable::Scroller {
                color: TEAL,
                border: border::Border::default(),
            },
        },
        horizontal_rail: iced::widget::scrollable::Rail {
            background: None,
            border: border::Border::default(),
            scroller: iced::widget::scrollable::Scroller {
                color: TEAL,
                border: border::Border::default(),
            },
        },
    }
}

// Tooltip with panel style
pub fn tooltip() -> impl Fn(&theme::Theme) -> iced::widget::container::Style {
    |_| iced::widget::container::Style {
        background: Some(Background::Color(PANEL)),
        text_color: Some(TEXT),
        border: border::Border {
            color: PANEL_EDGE,
            width: 1.0,
            radius: border::Radius::from(6.0),
        },
        ..Default::default()
    }
}

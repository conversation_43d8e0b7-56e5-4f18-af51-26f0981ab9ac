use iced::{
    widget::{column, pick_list, row, scrollable, text, text_input, text_input::Id},
    Alignment, Element, Length,
};

use crate::{
    icons,
    models::{LlmMessage, LlmState, Message, PickerOption, Provider, View},
    theme,
};

use super::components::{create_button, build_error_display};

pub fn build_llm_view<'a>(state: &'a LlmState, is_loading: bool, error: &'a str) -> Element<'a, Message> {
    column![
        text("YUMLABS").size(15),
        build_provider_model_pickers(state),
        build_input_field(state, is_loading),
        build_button_controls(is_loading),
        text("Response:").size(14),
        build_response_display(state, is_loading),
        build_error_display(error),
    ]
    .spacing(15)
    .padding(20)
    .align_x(Alignment::Center)
    .into()
}

fn build_provider_model_pickers(state: &'_ LlmState) -> Element<'_, Message> {
    let providers: Vec<_> = [Provider::Perplexity, Provider::Gemini]
        .iter()
        .map(|p| PickerOption::new(*p))
        .collect();
    let selected_provider = providers
        .iter()
        .find(|p| p.value == state.provider)
        .cloned();
    let provider_picker = pick_list(providers, selected_provider, |p| {
        Message::Llm(LlmMessage::ProviderChanged(p.value))
    })
    .style(theme::picker())
    .menu_style(theme::picker_menu());

    let models: Vec<_> = state
        .provider
        .available_models()
        .into_iter()
        .map(PickerOption::new)
        .collect();
    let selected_model = models.iter().find(|m| m.value == state.model).cloned();
    let model_picker = pick_list(models, selected_model, |m| {
        Message::Llm(LlmMessage::ModelChanged(m.value))
    })
        .style(theme::picker())
        .menu_style(theme::picker_menu());

    column![
        row![text("Provider:").size(14), provider_picker]
            .spacing(10)
            .align_y(Alignment::Center),
        row![text("Model:").size(14), model_picker]
            .spacing(10)
            .align_y(Alignment::Center),
    ]
    .spacing(10)
    .into()
}

fn build_input_field(state: &'_ LlmState, is_loading: bool) -> Element<'_, Message> {
    text_input("Type your question...", &state.query)
        .id(Id::new("query_input"))
        .on_input(if is_loading {
            |_| Message::Ignore
        } else {
            |s| Message::Llm(LlmMessage::QueryChanged(s))
        })
        .on_submit(if is_loading {
            Message::Ignore
        } else {
            Message::Llm(LlmMessage::Send)
        })
        .padding(10)
        .size(16)
        .width(Length::Fill)
        .style(theme::input())
        .into()
}

fn build_button_controls(is_loading: bool) -> Element<'static, Message> {
    let send_btn = create_button(
        icons::send(),
        "Send Query",
        if is_loading {
            None
        } else {
            Some(Message::Llm(LlmMessage::Send))
        },
        theme::panel_button(),
    );

    let copy_btn = create_button(
        icons::copy(),
        "Copy Response",
        Some(Message::Copy),
        theme::panel_button(),
    );

    let clear_btn = create_button(
        icons::clear(),
        "Clear All",
        Some(Message::Clear),
        theme::panel_button(),
    );

    let close_btn = create_button(
        icons::close(),
        "Close App",
        Some(Message::Close),
        theme::panel_button(),
    );

    let twitch_btn = create_button(
        icons::twitch(),
        "Open Twitch",
        Some(Message::Twitch),
        theme::panel_button(),
    );

    let perplexity_btn = create_button(
        icons::perplexity(),
        "Open Perplexity",
        Some(Message::Perplexity),
        theme::panel_button(),
    );
    let claude_btn = create_button(
        icons::claude(),
        "Open Claude",
        Some(Message::Claude),
        theme::panel_button(),
    );
    let github_btn = create_button(
        icons::github(),
        "Open GitHub",
        Some(Message::Github),
        theme::panel_button(),
    );
    let youtube_btn = create_button(
        icons::youtube(),
        "Open YouTube",
        Some(Message::Youtube),
        theme::panel_button(),
    );
    let kick_btn = create_button(
        icons::kick(),
        "Open Kick",
        Some(Message::Kick),
        theme::panel_button(),
    );

    let tools_btn = create_button(
        icons::tools(),
        "Open Tools",
        Some(Message::Navigate(View::Tools)),
        theme::panel_button(),
    );

    let primary_controls = row![send_btn, copy_btn, clear_btn, tools_btn, close_btn]
        .spacing(10)
        .align_y(Alignment::Center);

    let secondary_controls = row![
        twitch_btn,
        perplexity_btn,
        claude_btn,
        github_btn,
        youtube_btn,
        kick_btn
    ]
    .spacing(10)
    .align_y(Alignment::Center);

    column![primary_controls, secondary_controls]
        .spacing(10)
        .align_x(Alignment::Center)
        .into()
}

fn build_response_display(state: &'_ LlmState, is_loading: bool) -> Element<'_, Message> {
    let response_text = if is_loading {
        "  Loading...".to_string()
    } else if !state.response.is_empty() {
        state.response.clone()
    } else {
        "  Response will appear here...".to_string()
    };

    scrollable(text(response_text).size(14))
        .height(Length::Fixed(250.0))
        .width(Length::Fill)
        .style(theme::scroller())
        .into()
}
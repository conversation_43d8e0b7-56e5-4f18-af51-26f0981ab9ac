use crate::mongo_connection::get_todo_collection as get_collection;
use chrono::{DateTime, Utc};
use futures::stream::TryStreamExt;
use mongodb::bson::doc;
use mongodb::bson::oid::ObjectId;
use serde::{Deserialize, Serialize};

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct Todo {
    #[serde(rename = "_id", skip_serializing_if = "Option::is_none")]
    pub id: Option<ObjectId>,
    pub title: String,
    pub description: String,
    pub completed: bool,
    pub created_at: DateTime<Utc>,
}

impl Todo {
    pub fn new(title: String, description: String) -> Self {
        Todo {
            id: None,
            title,
            description,
            completed: false,
            created_at: Utc::now(),
        }
    }
}

pub async fn create_todo(title: String, description: String) -> mongodb::error::Result<()> {
    let collection = get_collection().await?;
    let todo = Todo::new(title, description);
    let result = collection.insert_one(todo, None).await?;
    println!("Created todo with id: {}", result.inserted_id);
    Ok(())
}

pub async fn list_incomplete_todos() -> mongodb::error::Result<Vec<Todo>> {
    let collection = get_collection().await?;
    let filter = doc! { "completed": false };
    let todos = collection.find(filter, None).await?.try_collect().await?;

    Ok(todos)
}

pub async fn complete_todo(id: ObjectId) -> mongodb::error::Result<()> {
    let collection = get_collection().await?;
    let filter = doc! { "_id": id };
    let update = doc! { "$set": { "completed": true } };

    let result = collection.update_one(filter, update, None).await?;

    if result.matched_count > 0 {
        println!("Todo marked as complete!");
    } else {
        println!("Todo not found.");
    }

    Ok(())
}

pub async fn delete_todo(id: ObjectId) -> mongodb::error::Result<()> {
    let collection = get_collection().await?;
    let filter = doc! { "_id": id };
    let result = collection.delete_one(filter, None).await?;

    if result.deleted_count > 0 {
        println!("Todo deleted successfully!");
    } else {
        println!("Todo not found.");
    }

    Ok(())
}

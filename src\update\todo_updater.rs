use crate::{
    models::{AppState, Message, TodoMessage},
    todo,
};
use iced::Task;

pub fn todo_update(state: &mut AppState, message: TodoMessage) -> Task<Message> {
    match message {
        TodoMessage::Load => {
            state.is_loading = true;
            Task::perform(async { todo::list_incomplete_todos().await }, |res| {
                Message::Todo(TodoMessage::Loaded(res.map_err(|e| e.to_string())))
            })
        }
        TodoMessage::Loaded(Ok(todos)) => {
            state.todo_state.todos = todos;
            state.is_loading = false;
            state.error.clear();
            Task::none()
        }
        TodoMessage::Loaded(Err(e)) => {
            state.error = format!("Failed to load todos: {}", e);
            state.is_loading = false;
            Task::none()
        }
        TodoMessage::NewTitleChanged(title) => {
            state.todo_state.new_todo_title = title;
            Task::none()
        }
        TodoMessage::Add => {
            let title = state.todo_state.new_todo_title.clone();
            state.todo_state.new_todo_title.clear();
            Task::perform(todo::create_todo(title, "".to_string()), |_| {
                Message::Todo(TodoMessage::Load)
            })
        }
        TodoMessage::ToggleCompletion(id) => {
            Task::perform(todo::complete_todo(id), |_| Message::Todo(TodoMessage::Load))
        }
        TodoMessage::Delete(id) => {
            Task::perform(todo::delete_todo(id), |_| Message::Todo(TodoMessage::Load))
        }
    }
}
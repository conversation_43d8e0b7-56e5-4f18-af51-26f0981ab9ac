use iced::{
    widget::{column, row, text},
    Alignment, Element,
};

use crate::{
    icons,
    models::{Message, View},
    theme,
};

use super::components::create_button;

pub fn build_tools_view<'a>() -> Element<'a, Message> {
    let home_button = create_button(
        icons::close(), // Using close icon for "back"
        "Back to Main",
        Some(Message::Navigate(View::Llm)),
        theme::panel_button(),
    );

    let vs_code_button = create_button(
        icons::tools(),
        "Open VS Code",
        Some(Message::VSCode),
        theme::panel_button(),
    );

    let todos_btn = create_button(
        icons::tools(), // Placeholder icon
        "Open Todos",
        Some(Message::Navigate(View::Todos)),
        theme::panel_button(),
    );

    let controls = vec![Element::from(todos_btn), Element::from(vs_code_button)];

    let grid = column(controls).spacing(5);

    column![row![home_button].align_y(Alignment::Center), text("Tools Grid").size(15), grid,]
        .spacing(15)
        .padding(20)
        .align_x(Alignment::Center)
        .into()
}
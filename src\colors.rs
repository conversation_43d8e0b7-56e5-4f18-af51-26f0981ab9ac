use iced::Color;

// Base colors
pub const TEXT: Color = Color::from_rgb(0.86, 0.88, 0.92);
pub const MUTED: Color = Color::from_rgba(0.78, 0.80, 0.86, 0.75);

// Theme colors
pub const PANEL: Color = Color::TRANSPARENT;
pub const PANEL_EDGE: Color = Color::from_rgba(0.30, 0.32, 0.38, 0.6);
pub const TEAL: Color = Color::from_rgb(0.39, 0.78, 0.95); // focus glow

// Component-specific colors
pub const PANEL_SHADOW: Color = Color::from_rgba(0.0, 0.0, 0.0, 0.55);
pub const BUTTON_BG: Color = Color::from_rgba(0.17, 0.18, 0.23, 0.95);
pub const BUTTON_SHADOW: Color = Color::from_rgba(0.0, 0.0, 0.0, 0.45);
pub const INPUT_BG: Color = Color::from_rgba(0.14, 0.15, 0.20, 0.9);
pub const SELECTION: Color = Color::from_rgba(0.39, 0.78, 0.95, 0.35);
pub const SCROLLER_BG: Color = Color::from_rgba(0.12, 0.13, 0.18, 0.9);
mod components;
mod llm_view;
mod tools_view;
mod todo_view;

use crate::models::*;
use crate::theme;
use iced::widget::container;
use iced::{Element, Length};

use self::llm_view::build_llm_view;
use self::tools_view::build_tools_view;
use self::todo_view::build_todo_view;

pub fn view(state: &'_ AppState) -> Element<'_, Message> {
    let content = match state.current_view {
        View::Llm => build_llm_view(&state.llm_state, state.is_loading, &state.error),
        View::Tools => build_tools_view(),
        View::Todos => build_todo_view(&state.todo_state, state.is_loading, &state.error),
    };
    container(content)
        .width(Length::Fill)
        .height(Length::Fill)
        .center_x(Length::Fill)
        .center_y(Length::Fill)
        .style(theme::panel())
        .into()
}

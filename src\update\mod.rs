mod llm_updater;
mod todo_updater;

use crate::models::*;
use arboard::Clipboard;
use iced::Task;
use std::process::{Command, Stdio};

use self::{llm_updater::llm_update, todo_updater::todo_update};

pub fn update(state: &mut AppState, message: Message) -> Task<Message> {
    match message {
        Message::Llm(llm_message) => llm_update(state, llm_message),
        Message::Todo(todo_message) => todo_update(state, todo_message),

        Message::Navigate(view) => {
            state.current_view = view;
            if view == View::Todos {
                // Trigger loading todos when navigating to the Todos view
                return todo_update(state, TodoMessage::Load);
            } else {
                state.error.clear(); // Clear errors when navigating away
                Task::none()
            }
        }
        Message::Copy => {
            if !state.llm_state.response.is_empty() {
                if let Ok(mut clipboard) = Clipboard::new() {
                    let _ = clipboard.set_text(state.llm_state.response.clone());
                    std::process::exit(0);
                }
            }
            Task::none()
        }
        Message::Clear => {
            state.llm_state.query.clear();
            state.llm_state.response.clear();
            state.error.clear();
            Task::none()
        }
        Message::Close => {
            std::process::exit(0);
        }
        Message::Ignore => Task::none(),
        Message::Twitch => open_url_in_brave("twitch.tv"),
        Message::Perplexity => open_url_in_brave("perplexity.ai"),
        Message::Claude => open_url_in_brave("claude.ai"),
        Message::Youtube => open_url_in_brave("youtube.com"),
        Message::Github => open_url_in_brave("github.com"),
        Message::Kick => open_url_in_brave("kick.com/zombiebarricades"),
        Message::VSCode => open_vs_code(),
    }
}

fn open_vs_code() -> Task<Message> {
    Task::perform(
        async move {
            #[cfg(target_os = "windows")]
            {
                Command::new("cmd").args(["/C", "code", "."]).spawn().ok();
            }
        },
        |_| Message::Ignore,
    )
}

/// Spawns a task to open a URL in Brave browser.
/// If Brave is not running, it will be started. If it is running, a new tab is opened.
fn open_url_in_brave(url: &'static str) -> Task<Message> {
    Task::perform(
        async move {
            #[cfg(target_os = "windows")]
            {
                Command::new("cmd")
                    .args(["/C", "start", "brave", url])
                    .stdout(Stdio::null())
                    .stderr(Stdio::null())
                    .spawn()
                    .ok();
                std::process::exit(0);
            }
        },
        |_| Message::Ignore,
    )
}

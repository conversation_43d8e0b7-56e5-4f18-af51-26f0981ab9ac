# Rules
- All tasks need to be put into the pending section of proposed.md before being worked on.
- Once a task is started, move it to the accepted section of proposed.md.
- Only once its move to accepted are you allowed to work on it and I have responded with the command [Execute the Plan].
- If a task is rejected, move it to the rejected section of proposed.md, The task is not to be worked on at this time, I will respond with the command [Reject Compare] when I want to work on a task that was rejected.
- You are only allowed to work on accepted tasks.
- You should always cargo check and allow the user to verify the changes
- If you ever make changes, you have to also make changes to the developer-doc

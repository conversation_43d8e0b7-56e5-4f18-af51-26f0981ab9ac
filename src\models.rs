use crate::todo::Todo;

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>q, <PERSON><PERSON>ult)]
pub enum View {
    #[default]
    Llm,
    Tools,
    Todos,
}

// --- View-specific states ---
#[derive(Default)]
pub struct LlmState {
    pub query: String,
    pub response: String,
    pub provider: Provider,
    pub model: String,
}

#[derive(Default)]
pub struct TodoState {
    pub todos: Vec<Todo>,
    pub new_todo_title: String,
}

// --- Main Application State ---
#[derive(Default)]
pub struct AppState {
    pub current_view: View,
    pub llm_state: LlmState,
    pub todo_state: TodoState,
    pub is_loading: bool, // Global loading indicator
    pub error: String,      // Global error message
}

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, PartialEq, Eq, Default)]
pub enum Provider {
    #[default]
    Perplexity,
    Gemini,
}

impl Provider {
    pub fn available_models(&self) -> Vec<String> {
        match self {
            Provider::Perplexity => vec!["sonar".to_string(), "sonar-pro".to_string()],
            Provider::Gemini => vec![
               "gemini-flash-latest",
               "gemini-flash-lite-latest",
               "gemini-2.5-flash",
               "gemini-2.5-flash-lite",
               "gemini-2.5-pro",
               "gemini-2.5-flash-image-preview"
            ]
            .into_iter()
            .map(|s| s.to_string())
            .collect(),
        }
    }

    pub fn default_model(&self) -> String {
        self.available_models()[0].clone()
    }
}

impl std::fmt::Display for Provider {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            Provider::Perplexity => write!(f, "Perplexity"),
            Provider::Gemini => write!(f, "Gemini"),
        }
    }
}

/// A wrapper for pick list options to allow for custom display formatting (e.g., uppercase)
/// while retaining the original value.
#[derive(Clone, Eq, PartialEq)]
pub struct PickerOption<T> {
    pub value: T,
    display: String,
}

impl<T: ToString> PickerOption<T> {
    pub fn new(value: T) -> Self {
        let display = value.to_string().to_uppercase();
        Self { value, display }
    }
}

impl<T> std::fmt::Display for PickerOption<T> {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "{}", self.display)
    }
}

// --- Nested Messages for better organization ---

#[derive(Debug, Clone)]
pub enum LlmMessage {
    QueryChanged(String),
    ProviderChanged(Provider),
    ModelChanged(String),
    Send,
    ResponseReceived(Result<String, String>),
}

#[derive(Debug, Clone)]
pub enum TodoMessage {
    Load,
    Loaded(Result<Vec<Todo>, String>),
    NewTitleChanged(String),
    Add,
    ToggleCompletion(mongodb::bson::oid::ObjectId),
    Delete(mongodb::bson::oid::ObjectId),
}

#[derive(Debug, Clone)]
pub enum Message {
    // View Navigation
    Navigate(View),
    // Global Actions
    Twitch,
    Perplexity,
    Claude,
    Youtube,
    Github,
    VSCode,
    Kick,
    Copy,
    Clear,
    Close,
    Ignore,
    // Sub-messages
    Llm(LlmMessage),
    Todo(TodoMessage),
}

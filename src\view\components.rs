use iced::{
    widget::{button, image, text, tooltip},
    Element,
};

use crate::{models::Message, theme};

pub fn create_button<'a>(
    icon: image::Image,
    tooltip_text: &'static str,
    on_press: Option<Message>,
    style: impl Fn(&iced::Theme, button::Status) -> button::Style + 'a,
) -> Element<'a, Message> {
    let btn = button(icon).padding(10).height(50).style(style);

    let btn_with_press = if let Some(msg) = on_press {
        btn.on_press(msg)
    } else {
        btn
    };

    tooltip(btn_with_press, tooltip_text, tooltip::Position::Bottom)
        .style(theme::tooltip())
        .into()
}

pub fn build_error_display(error: &'_ str) -> Element<'_, Message> {
    if !error.is_empty() {
        text(error)
            .size(14)
            .style(|_theme| text::Style {
                color: Some(iced::Color::from_rgb(0.8, 0.2, 0.2)),
            })
            .into()
    } else {
        text("").into()
    }
}
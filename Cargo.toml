[package]
name = "yumlabs"
version = "0.1.0"
edition = "2021"

[dependencies]
iced = { version = "0.13", features = ["tokio", "image"] }  # For async support
reqwest = { version = "0.12", features = ["json"] }
serde = { version = "1.0", features = ["derive"] }
arboard = "3.4.0"  # For clipboard
tokio = { version = "1", features = ["full"] }  # Async runtime for reqwest
dotenv = "0.15.0"
sysinfo = "0.30"
mongodb = "2.8"
chrono = { version = "0.4", features = ["serde"] }
futures = "0.3.30"
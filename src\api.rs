use dotenv::dotenv;
use serde::{Deserialize, Serialize};
use std::env;

use chrono::{DateTime, Utc};
use mongodb::bson::doc;
use mongodb::bson::oid::ObjectId;

use crate::mongo_connection::get_llm_collection;

fn get_api_key(provider: String) -> Result<String, String> {
    dotenv().ok();
    for (key, value) in env::vars() {
        if key == "PERPLEXITY_API_KEY" && provider == "perplexity" {
            return Ok(value);
        }
        if key == "GEMINI_API_KEY" && provider == "gemini" {
            return Ok(value);
        }
    }
    Err(format!("{} API key not set", provider))
}


#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LlmSave {
    #[serde(rename = "_id", skip_serializing_if = "Option::is_none")]
    pub id: Option<ObjectId>,
    pub query: String,
    pub response: String,
    pub provider: String,
    pub model: String,
    pub created_at: DateTime<Utc>,
}

impl LlmSave {
    pub fn new(query: String, response: String, provider: String, model: String) -> Self {
        LlmSave {
                id: None,
                query,
                response,
                provider,
                model,
                created_at: Utc::now(),
        }
    }
}

pub async fn save_llm_response(query: String, response: String, provider: String, model: String) -> Result<(), String> {
    let collection = get_llm_collection().await.unwrap();
    let response = LlmSave::new(query, response, provider, model);
    let result = collection.insert_one(&response, None).await.unwrap();
    println!("Saved response with id: {}", result.inserted_id);
    Ok(())
}



#[derive(Serialize)]
pub struct ChatCompletionRequest {
    pub model: String,
    pub messages: Vec<MessageReq>,
}

#[derive(Serialize)]
pub struct MessageReq {
    pub role: String,
    pub content: String,
}

#[derive(Deserialize)]
pub struct ChatCompletionResponse {
    pub choices: Vec<Choice>,
}

#[derive(Deserialize)]
pub struct Choice {
    pub message: MessageResp,
}

#[derive(Deserialize)]
pub struct MessageResp {
    pub content: String,
}

#[derive(Serialize)]
pub struct GeminiRequest {
    pub contents: Vec<GeminiContent>,
}

#[derive(Serialize)]
pub struct GeminiContent {
    pub parts: Vec<GeminiPart>,
}

#[derive(Serialize)]
pub struct GeminiPart {
    pub text: String,
}

#[derive(Deserialize)]
pub struct GeminiResponse {
    pub candidates: Vec<GeminiCandidate>,
}

#[derive(Deserialize)]
pub struct GeminiCandidate {
    pub content: GeminiContentResp,
}

#[derive(Deserialize)]
pub struct GeminiContentResp {
    pub parts: Vec<GeminiPartResp>,
}

#[derive(Deserialize)]
pub struct GeminiPartResp {
    pub text: String,
}

pub async fn fetch_llm_response(
    query: String,
    provider: crate::models::Provider,
    model: String,
) -> Result<String, String> {
    dotenv().ok();
    let client = reqwest::Client::new();

    match provider {
        crate::models::Provider::Perplexity => {
            let api_key = get_api_key("perplexity".to_string())?.to_string();

            let body = ChatCompletionRequest {
                model,
                messages: vec![MessageReq {
                    role: "user".to_string(),
                    content: query,
                }],
            };

            let resp = client
                .post("https://api.perplexity.ai/chat/completions")
                .header(
                    reqwest::header::AUTHORIZATION,
                    format!("Bearer {}", api_key),
                )
                .header(reqwest::header::CONTENT_TYPE, "application/json")
                .json(&body)
                .send()
                .await
                .map_err(|e| format!("Request failed: {}", e))?;

            if resp.status().is_success() {
                let completion: ChatCompletionResponse = resp
                    .json()
                    .await
                    .map_err(|e| format!("Failed to parse response: {}", e))?;

                if completion.choices.is_empty() {
                    return Err("No response from Perplexity".to_string());
                }

                Ok(completion.choices[0].message.content.clone())
            } else {
                let status = resp.status();
                let error_text = resp.text().await.unwrap_or_default();
                Err(format!("Perplexity API error {}: {}", status, error_text))
            }
        }
        crate::models::Provider::Gemini => {
            let api_key = get_api_key("gemini".to_string())?.to_string();

            let body = GeminiRequest {
                contents: vec![GeminiContent {
                    parts: vec![GeminiPart { text: query }],
                }],
            };

            let url = format!(
                "https://generativelanguage.googleapis.com/v1beta/models/{}:generateContent",
                model
            );

            let resp = client
                .post(&url)
                .query(&[("key", &api_key)])
                .header(reqwest::header::CONTENT_TYPE, "application/json")
                .json(&body)
                .send()
                .await
                .map_err(|e| format!("Request failed: {}", e))?;

            if resp.status().is_success() {
                let completion: GeminiResponse = resp
                    .json()
                    .await
                    .map_err(|e| format!("Failed to parse response: {}", e))?;

                if completion.candidates.is_empty() {
                    return Err("No response from Gemini".to_string());
                }

                Ok(completion.candidates[0].content.parts[0].text.clone())
            } else {
                let status = resp.status();
                let error_text = resp.text().await.unwrap_or_default();
                Err(format!("Gemini API error {}: {}", status, error_text))
            }
        }
    }
}

